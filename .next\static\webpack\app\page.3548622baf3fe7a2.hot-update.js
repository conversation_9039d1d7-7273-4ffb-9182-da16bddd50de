"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, alt, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"parallax-wrap relative aspect-[16/9] rounded-md overflow-hidden mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: image,\n                        alt: alt !== null && alt !== void 0 ? alt : title,\n                        fill: true,\n                        className: \"parallax-img object-cover will-change-transform\",\n                        priority: false\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] leading-none tracking-[.02em] uppercase flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8 text-center md:text-left\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 53\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const wrapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // reveal + parallax hover for images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const section = sectionRef.current;\n        if (!section) return;\n        // reveal only children (not containers)\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(section.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n        // image hover parallax on desktop pointers\n        const cards = Array.from(section.querySelectorAll(\".parallax-wrap\"));\n        const enter = (img)=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                scale: 1.04,\n                duration: 0.35,\n                ease: \"power3.out\"\n            });\n        };\n        const leave = (img)=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: 0,\n                y: 0,\n                rotate: 0,\n                scale: 1,\n                duration: 0.45,\n                ease: \"power3.out\"\n            });\n        };\n        const move = (img, e)=>{\n            const rect = img.parentElement.getBoundingClientRect();\n            const mx = (e.clientX - rect.left) / rect.width - 0.5 // -0.5..0.5\n            ;\n            const my = (e.clientY - rect.top) / rect.height - 0.5;\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: mx * 14,\n                y: my * 14,\n                rotate: mx * 1.2,\n                overwrite: true,\n                duration: 0.25,\n                ease: \"power2.out\"\n            });\n        };\n        const handlers = [];\n        cards.forEach((card)=>{\n            const img = card.querySelector(\".parallax-img\");\n            if (!img) return;\n            const onEnter = ()=>enter(img);\n            const onLeave = ()=>leave(img);\n            const onMove = (e)=>move(img, e);\n            card.addEventListener(\"mouseenter\", onEnter);\n            card.addEventListener(\"mouseleave\", onLeave);\n            card.addEventListener(\"mousemove\", onMove);\n            handlers.push(()=>{\n                card.removeEventListener(\"mouseenter\", onEnter);\n                card.removeEventListener(\"mouseleave\", onLeave);\n                card.removeEventListener(\"mousemove\", onMove);\n            });\n        });\n        return ()=>handlers.forEach((fn)=>fn());\n    }, []);\n    // click-drag horizontal navigation for small/medium screens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = wrapRef.current;\n        if (!el) return;\n        let isDown = false;\n        let startX = 0;\n        let startScroll = 0;\n        const down = (e)=>{\n            isDown = true;\n            el.classList.add(\"dragging\");\n            startX = e.pageX - el.offsetLeft;\n            startScroll = el.scrollLeft;\n        };\n        const up = ()=>{\n            isDown = false;\n            el.classList.remove(\"dragging\");\n        };\n        const move = (e)=>{\n            if (!isDown) return;\n            e.preventDefault();\n            const x = e.pageX - el.offsetLeft;\n            const walk = x - startX // px dragged\n            ;\n            el.scrollLeft = startScroll - walk;\n        };\n        // Only enable the mouse drag on non-touch pointers\n        el.addEventListener(\"mousedown\", down);\n        window.addEventListener(\"mouseup\", up);\n        el.addEventListener(\"mousemove\", move);\n        // Allow native touch scrolling without listeners\n        return ()=>{\n            el.removeEventListener(\"mousedown\", down);\n            window.removeEventListener(\"mouseup\", up);\n            el.removeEventListener(\"mousemove\", move);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: wrapRef,\n            className: \"headerstrip-drag mx-auto max-w-6xl overflow-x-auto md:overflow-x-visible\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex md:grid md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-ink/20 min-w-[1100px] md:min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO interior\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO interior\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"UkKPphdwrBVszhnRXq3INm4Aen4=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});