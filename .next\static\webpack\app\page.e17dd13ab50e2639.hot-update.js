"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 6,\n        columnNumber: 30\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, alt, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"parallax-wrap relative aspect-[16/9] rounded-lg overflow-hidden mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: image,\n                        alt: alt !== null && alt !== void 0 ? alt : title,\n                        fill: true,\n                        className: \"parallax-img object-cover will-change-transform\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-35\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] uppercase leading-none tracking-[.02em] flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 52\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const wrapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const trackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // reveal + parallax\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const section = sectionRef.current;\n        if (!section) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(section.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n        const wraps = Array.from(section.querySelectorAll(\".parallax-wrap\"));\n        const enter = (img)=>_lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                scale: 1.04,\n                duration: 0.28,\n                ease: \"power3.out\"\n            });\n        const leave = (img)=>_lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: 0,\n                y: 0,\n                rotate: 0,\n                scale: 1,\n                duration: 0.4,\n                ease: \"power3.out\"\n            });\n        const move = (img, e)=>{\n            const r = img.parentElement.getBoundingClientRect();\n            const mx = (e.clientX - r.left) / r.width - 0.5;\n            const my = (e.clientY - r.top) / r.height - 0.5;\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: mx * 14,\n                y: my * 14,\n                rotate: mx * 1.2,\n                overwrite: true,\n                duration: 0.22,\n                ease: \"power2.out\"\n            });\n        };\n        const cleanups = [];\n        wraps.forEach((w)=>{\n            const img = w.querySelector(\".parallax-img\");\n            if (!img) return;\n            const onEnter = ()=>enter(img);\n            const onLeave = ()=>leave(img);\n            const onMove = (e)=>move(img, e);\n            w.addEventListener(\"mouseenter\", onEnter);\n            w.addEventListener(\"mouseleave\", onLeave);\n            w.addEventListener(\"mousemove\", onMove);\n            cleanups.push(()=>{\n                w.removeEventListener(\"mouseenter\", onEnter);\n                w.removeEventListener(\"mouseleave\", onLeave);\n                w.removeEventListener(\"mousemove\", onMove);\n            });\n        });\n        return ()=>cleanups.forEach((fn)=>fn());\n    }, []);\n    // drag / wheel-horizontal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const wrap = wrapRef.current;\n        const track = trackRef.current;\n        if (!wrap || !track) return;\n        // mouse drag\n        let isDown = false, startX = 0, startScroll = 0;\n        const down = (e)=>{\n            isDown = true;\n            wrap.classList.add(\"dragging\");\n            startX = e.pageX;\n            startScroll = wrap.scrollLeft;\n        };\n        const up = ()=>{\n            isDown = false;\n            wrap.classList.remove(\"dragging\");\n        };\n        const move = (e)=>{\n            if (!isDown) return;\n            e.preventDefault();\n            const walk = e.pageX - startX;\n            wrap.scrollLeft = startScroll - walk;\n        };\n        wrap.addEventListener(\"mousedown\", down);\n        window.addEventListener(\"mouseup\", up);\n        wrap.addEventListener(\"mousemove\", move);\n        // wheel to horizontal (trackpads / mouse wheels)\n        const onWheel = (e)=>{\n            if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {\n                wrap.scrollLeft += e.deltaY;\n                e.preventDefault();\n            }\n        };\n        wrap.addEventListener(\"wheel\", onWheel, {\n            passive: false\n        });\n        return ()=>{\n            wrap.removeEventListener(\"mousedown\", down);\n            window.removeEventListener(\"mouseup\", up);\n            wrap.removeEventListener(\"mousemove\", move);\n            wrap.removeEventListener(\"wheel\", onWheel);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: wrapRef,\n            className: \"headerstrip-drag mx-auto max-w-6xl overflow-x-auto md:overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: trackRef,\n                className: \"headerstrip-track flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel border-l first:border-l-0 border-ink/20\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small-3.jpeg\",\n                                alt: \"AvroKO\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"AvroKO\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel border-l border-ink/20\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel border-l border-ink/20\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"YEid6S/J1S3DRFT/85aiTGMpaXI=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});