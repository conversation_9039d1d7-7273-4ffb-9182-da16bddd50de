'use client'
import { useEffect, useRef } from 'react'
import Image from 'next/image'
import { gsap, Draggable } from '@/lib/gsap'

type Tile =
  | { kind: 'card'; title: string; image: string; body: string }
  | { kind: 'copy' }

const tiles: Tile[] = [
  { kind: 'card', title: 'AvroKO', image: '/hero/thumbnail-small-3.jpeg',
    body: `AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.` },
  { kind: 'copy' },
  { kind: 'card', title: 'The Roger Hub', image: '/hero/thumbnail-small-1.jpeg',
    body: `The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary <PERSON>.` },
  // add a couple extra tiles so the rail definitely overflows on desktop
  { kind: 'card', title: 'WOW Concept', image: '/hero/thumbnail-small-3.jpeg',
    body: `A concept store experience in Madrid revolutionizing retail with dynamic, interactive shopping.` },
  { kind: 'card', title: 'Unexpected Time', image: '/hero/thumbnail-small-1.jpeg',
    body: `Timeless design & details – a creative portfolio that evolves with modern functionality.` },
]

export default function HeaderStrip() {
  const viewportRef = useRef<HTMLDivElement>(null)
  const railRef = useRef<HTMLDivElement>(null)

  // DRAGGABLE rail with snap
  useEffect(() => {
    const viewport = viewportRef.current!
    const rail = railRef.current!
    const tilesEls = Array.from(rail.querySelectorAll<HTMLElement>('.hs-tile'))

    // set tile widths so the rail always exceeds viewport width
    // (CSS also handles this, but we clamp again here after fonts load)
    const setWidths = () => {
      tilesEls.forEach(t => {
        // nothing to compute; CSS flex-basis handles it, but we keep for clarity
      })
    }

    // position rail at start
    gsap.set(rail, { x: 0 })

    const minX = () => Math.min(0, viewport.clientWidth - rail.scrollWidth)

    const draggable = Draggable.create(rail, {
      type: 'x',
      bounds: { minX: minX(), maxX: 0 },
      inertia: false,           // free GSAP
      dragClickables: true,
      onPress()   { viewport.classList.add('dragging') },
      onRelease() { viewport.classList.remove('dragging'); snapToClosest() },
    })[0]

    // snap to nearest tile so a tile aligns to the left edge
    function snapToClosest() {
      const x = draggable.x
      let best = 0, min = Infinity
      for (const t of tilesEls) {
        const goal = -t.offsetLeft   // left-align this tile
        const d = Math.abs(x - goal)
        if (d < min) { min = d; best = goal }
      }
      const target = gsap.utils.clamp(minX(), 0, best)
      gsap.to(rail, { x: target, duration: 0.35, ease: 'power3.out', onUpdate: () => draggable.update() })
    }

    // wheel → horizontal (great on trackpads)
    let wheelSnapTimer: any
    const onWheel = (e: WheelEvent) => {
      if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
        const next = gsap.utils.clamp(minX(), 0, draggable.x - e.deltaY)
        gsap.to(rail, { x: next, duration: 0.12, ease: 'power2.out', onUpdate: () => draggable.update() })
        e.preventDefault()
        clearTimeout(wheelSnapTimer)
        wheelSnapTimer = setTimeout(snapToClosest, 140)
      }
    }
    viewport.addEventListener('wheel', onWheel, { passive: false })

    // keep bounds correct on resize/font load
    const ro = new ResizeObserver(() => {
      draggable.applyBounds({ minX: minX(), maxX: 0 })
    })
    ro.observe(viewport); ro.observe(rail)
    setWidths()

    return () => {
      viewport.removeEventListener('wheel', onWheel as any)
      ro.disconnect()
      draggable.kill()
    }
  }, [])

  return (
    <section className="no-wash relative z-[3] border-y border-ink/15">
      {/* Viewport (fixed width, overflow hidden) */}
      <div ref={viewportRef} className="hs-viewport mx-auto max-w-6xl overflow-hidden">
        {/* Rail (wide flex row we drag left/right) */}
        <div ref={railRef} className="hs-rail flex gap-0 select-none">
          {tiles.map((t, i) => (
            <div key={i} className={`hs-tile border-l first:border-l-0 border-ink/20`}>
              {t.kind === 'copy' ? (
                <div className="px-4 md:px-6 py-8">
                  <div className="font-display text-5xl md:text-[56px] leading-[0.95]">ALL WORK!</div>
                  <div className="font-display text-[26px] md:text-[30px] leading-[1.05] mt-3">
                    A Featured selection<br/> the latest work —<br/> of the last years.
                  </div>
                  <div className="mt-4 text-[12px] uppercase tracking-wider text-ink/70">
                    <strong>Tip!</strong> Drag sideways to navigate
                  </div>
                </div>
              ) : (
                <article className="px-4 md:px-6 py-8">
                  <div className="relative aspect-[16/9] rounded-lg overflow-hidden mb-4">
                    <Image src={t.image} alt={t.title} fill className="object-cover" />
                    <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-35" />
                  </div>
                  <h4 className="font-display text-[22px] uppercase leading-none tracking-[.02em] flex items-center gap-2">
                    {t.title} <span className="badge-new">NEW</span>
                  </h4>
                  <p className="mt-2 text-[15px] leading-6 text-ink/80">{t.body}</p>
                </article>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
