"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 6,\n        columnNumber: 29\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, alt, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"parallax-wrap relative aspect-[16/9] rounded-lg overflow-hidden mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: image,\n                        alt: alt !== null && alt !== void 0 ? alt : title,\n                        fill: true,\n                        className: \"parallax-img object-cover will-change-transform\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-35\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] uppercase leading-none tracking-[.02em] flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 52\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const wrapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // viewport\n    ;\n    const trackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // draggable row\n    ;\n    // reveal + image parallax\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const section = sectionRef.current;\n        if (!section) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(section.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n        // parallax hover (desktop)\n        const cards = Array.from(section.querySelectorAll(\".parallax-wrap\"));\n        const enter = (img)=>_lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                scale: 1.04,\n                duration: 0.28,\n                ease: \"power3.out\"\n            });\n        const leave = (img)=>_lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: 0,\n                y: 0,\n                rotate: 0,\n                scale: 1,\n                duration: 0.4,\n                ease: \"power3.out\"\n            });\n        const move = (img, e)=>{\n            const r = img.parentElement.getBoundingClientRect();\n            const mx = (e.clientX - r.left) / r.width - 0.5;\n            const my = (e.clientY - r.top) / r.height - 0.5;\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: mx * 14,\n                y: my * 14,\n                rotate: mx * 1.2,\n                overwrite: true,\n                duration: 0.22,\n                ease: \"power2.out\"\n            });\n        };\n        const off = [];\n        cards.forEach((card)=>{\n            const img = card.querySelector(\".parallax-img\");\n            const onEnter = ()=>enter(img);\n            const onLeave = ()=>leave(img);\n            const onMove = (e)=>move(img, e);\n            card.addEventListener(\"mouseenter\", onEnter);\n            card.addEventListener(\"mouseleave\", onLeave);\n            card.addEventListener(\"mousemove\", onMove);\n            off.push(()=>{\n                card.removeEventListener(\"mouseenter\", onEnter);\n                card.removeEventListener(\"mouseleave\", onLeave);\n                card.removeEventListener(\"mousemove\", onMove);\n            });\n        });\n        return ()=>off.forEach((fn)=>fn());\n    }, []);\n    // GSAP Draggable row with snap + wheel->horizontal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const wrap = wrapRef.current;\n        const track = trackRef.current;\n        let snapTimer = null;\n        const panels = Array.from(track.querySelectorAll(\".panel\"));\n        const getMinX = ()=>Math.min(0, wrap.clientWidth - track.scrollWidth);\n        // set initial\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.set(track, {\n            x: 0\n        });\n        // create draggable\n        const draggable = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.Draggable.create(track, {\n            type: \"x\",\n            bounds: {\n                minX: getMinX(),\n                maxX: 0\n            },\n            inertia: false,\n            dragClickables: true,\n            onPress () {\n                wrap.classList.add(\"dragging\");\n            },\n            onRelease () {\n                wrap.classList.remove(\"dragging\");\n                snapToClosest();\n            }\n        })[0];\n        // snap to closest panel\n        function snapToClosest() {\n            const x = draggable.x;\n            let best = 0, min = Infinity;\n            for (const p of panels){\n                const goal = -p.offsetLeft // move so this panel aligns to left\n                ;\n                const d = Math.abs(x - goal);\n                if (d < min) {\n                    min = d;\n                    best = goal;\n                }\n            }\n            const target = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.clamp(getMinX(), 0, best);\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(track, {\n                x: target,\n                duration: 0.35,\n                ease: \"power3.out\",\n                onUpdate: ()=>draggable.update()\n            });\n        }\n        // on resize, recompute bounds & (optionally) re-snap\n        const ro = new ResizeObserver(()=>{\n            draggable.applyBounds({\n                minX: getMinX(),\n                maxX: 0\n            });\n            snapToClosest();\n        });\n        ro.observe(wrap);\n        ro.observe(track);\n        // wheel -> horizontal (good for trackpads)\n        const onWheel = (e)=>{\n            if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {\n                const minX = getMinX();\n                const next = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.clamp(minX, 0, draggable.x - e.deltaY);\n                _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(track, {\n                    x: next,\n                    duration: 0.15,\n                    ease: \"power2.out\",\n                    onUpdate: ()=>draggable.update()\n                });\n                e.preventDefault();\n                if (snapTimer) clearTimeout(snapTimer);\n                snapTimer = setTimeout(snapToClosest, 160);\n            }\n        };\n        wrap.addEventListener(\"wheel\", onWheel, {\n            passive: false\n        });\n        return ()=>{\n            ro.disconnect();\n            wrap.removeEventListener(\"wheel\", onWheel);\n            draggable.kill();\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: wrapRef,\n            className: \"headerstrip-viewport mx-auto max-w-6xl overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: trackRef,\n                className: \"headerstrip-track flex select-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel shrink-0 border-l first:border-l-0 border-ink/20\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel shrink-0 border-l border-ink/20\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel shrink-0 border-l border-ink/20\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"YEid6S/J1S3DRFT/85aiTGMpaXI=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});