"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 6,\n        columnNumber: 29\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, alt, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"parallax-wrap relative aspect-[16/9] rounded-lg overflow-hidden mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: image,\n                        alt: alt !== null && alt !== void 0 ? alt : title,\n                        fill: true,\n                        className: \"parallax-img object-cover will-change-transform\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-35\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] uppercase leading-none tracking-[.02em] flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 52\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const wrapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // viewport\n    ;\n    const trackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // draggable row\n    ;\n    // reveal + image parallax\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const section = sectionRef.current;\n        if (!section) return;\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(section.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n        // parallax hover (desktop)\n        const cards = Array.from(section.querySelectorAll(\".parallax-wrap\"));\n        const enter = (img)=>_lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                scale: 1.04,\n                duration: 0.28,\n                ease: \"power3.out\"\n            });\n        const leave = (img)=>_lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: 0,\n                y: 0,\n                rotate: 0,\n                scale: 1,\n                duration: 0.4,\n                ease: \"power3.out\"\n            });\n        const move = (img, e)=>{\n            const r = img.parentElement.getBoundingClientRect();\n            const mx = (e.clientX - r.left) / r.width - 0.5;\n            const my = (e.clientY - r.top) / r.height - 0.5;\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: mx * 14,\n                y: my * 14,\n                rotate: mx * 1.2,\n                overwrite: true,\n                duration: 0.22,\n                ease: \"power2.out\"\n            });\n        };\n        const off = [];\n        cards.forEach((card)=>{\n            const img = card.querySelector(\".parallax-img\");\n            const onEnter = ()=>enter(img);\n            const onLeave = ()=>leave(img);\n            const onMove = (e)=>move(img, e);\n            card.addEventListener(\"mouseenter\", onEnter);\n            card.addEventListener(\"mouseleave\", onLeave);\n            card.addEventListener(\"mousemove\", onMove);\n            off.push(()=>{\n                card.removeEventListener(\"mouseenter\", onEnter);\n                card.removeEventListener(\"mouseleave\", onLeave);\n                card.removeEventListener(\"mousemove\", onMove);\n            });\n        });\n        return ()=>off.forEach((fn)=>fn());\n    }, []);\n    // GSAP Draggable row with snap + wheel->horizontal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const wrap = wrapRef.current;\n        const track = trackRef.current;\n        let snapTimer = null;\n        const panels = Array.from(track.querySelectorAll(\".panel\"));\n        const getMinX = ()=>Math.min(0, wrap.clientWidth - track.scrollWidth);\n        // set initial\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.set(track, {\n            x: 0\n        });\n        // create draggable\n        const draggable = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.Draggable.create(track, {\n            type: \"x\",\n            bounds: {\n                minX: getMinX(),\n                maxX: 0\n            },\n            inertia: false,\n            dragClickables: true,\n            onPress () {\n                wrap.classList.add(\"dragging\");\n            },\n            onRelease () {\n                wrap.classList.remove(\"dragging\");\n                snapToClosest();\n            }\n        })[0];\n        // snap to closest panel\n        function snapToClosest() {\n            const x = draggable.x;\n            let best = 0, min = Infinity;\n            for (const p of panels){\n                const goal = -p.offsetLeft // move so this panel aligns to left\n                ;\n                const d = Math.abs(x - goal);\n                if (d < min) {\n                    min = d;\n                    best = goal;\n                }\n            }\n            const target = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.clamp(getMinX(), 0, best);\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(track, {\n                x: target,\n                duration: 0.35,\n                ease: \"power3.out\",\n                onUpdate: ()=>draggable.update()\n            });\n        }\n        // on resize, recompute bounds & (optionally) re-snap\n        const ro = new ResizeObserver(()=>{\n            draggable.applyBounds({\n                minX: getMinX(),\n                maxX: 0\n            });\n            snapToClosest();\n        });\n        ro.observe(wrap);\n        ro.observe(track);\n        // wheel -> horizontal (good for trackpads)\n        const onWheel = (e)=>{\n            if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {\n                const minX = getMinX();\n                const next = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.clamp(minX, 0, draggable.x - e.deltaY);\n                _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(track, {\n                    x: next,\n                    duration: 0.15,\n                    ease: \"power2.out\",\n                    onUpdate: ()=>{\n                        draggable.update();\n                    }\n                });\n                e.preventDefault();\n                if (snapTimer) clearTimeout(snapTimer);\n                snapTimer = setTimeout(snapToClosest, 160);\n            }\n        };\n        wrap.addEventListener(\"wheel\", onWheel, {\n            passive: false\n        });\n        return ()=>{\n            ro.disconnect();\n            wrap.removeEventListener(\"wheel\", onWheel);\n            draggable.kill();\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: wrapRef,\n            className: \"headerstrip-viewport mx-auto max-w-6xl overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: trackRef,\n                className: \"headerstrip-track flex select-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel shrink-0 border-l first:border-l-0 border-ink/20\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel shrink-0 border-l border-ink/20\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"panel shrink-0 border-l border-ink/20\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"YEid6S/J1S3DRFT/85aiTGMpaXI=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});