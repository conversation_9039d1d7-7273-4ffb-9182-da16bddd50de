"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, alt, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"parallax-wrap relative aspect-[16/9] rounded-md overflow-hidden mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: image,\n                        alt: alt !== null && alt !== void 0 ? alt : title,\n                        fill: true,\n                        className: \"parallax-img object-cover will-change-transform\",\n                        priority: false\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] leading-none tracking-[.02em] uppercase flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8 text-center md:text-left\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 53\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const wrapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // reveal + parallax hover for images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const section = sectionRef.current;\n        if (!section) return;\n        // reveal only children (not containers)\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(section.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n        // image hover parallax on desktop pointers\n        const cards = Array.from(section.querySelectorAll(\".parallax-wrap\"));\n        const enter = (img)=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                scale: 1.04,\n                duration: 0.35,\n                ease: \"power3.out\"\n            });\n        };\n        const leave = (img)=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: 0,\n                y: 0,\n                rotate: 0,\n                scale: 1,\n                duration: 0.45,\n                ease: \"power3.out\"\n            });\n        };\n        const move = (img, e)=>{\n            const rect = img.parentElement.getBoundingClientRect();\n            const mx = (e.clientX - rect.left) / rect.width - 0.5 // -0.5..0.5\n            ;\n            const my = (e.clientY - rect.top) / rect.height - 0.5;\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: mx * 14,\n                y: my * 14,\n                rotate: mx * 1.2,\n                overwrite: true,\n                duration: 0.25,\n                ease: \"power2.out\"\n            });\n        };\n        const handlers = [];\n        cards.forEach((card)=>{\n            const img = card.querySelector(\".parallax-img\");\n            if (!img) return;\n            const onEnter = ()=>enter(img);\n            const onLeave = ()=>leave(img);\n            const onMove = (e)=>move(img, e);\n            card.addEventListener(\"mouseenter\", onEnter);\n            card.addEventListener(\"mouseleave\", onLeave);\n            card.addEventListener(\"mousemove\", onMove);\n            handlers.push(()=>{\n                card.removeEventListener(\"mouseenter\", onEnter);\n                card.removeEventListener(\"mouseleave\", onLeave);\n                card.removeEventListener(\"mousemove\", onMove);\n            });\n        });\n        return ()=>handlers.forEach((fn)=>fn());\n    }, []);\n    // click-drag horizontal navigation for small/medium screens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = wrapRef.current;\n        if (!el) return;\n        let isDown = false;\n        let startX = 0;\n        let startScroll = 0;\n        const down = (e)=>{\n            isDown = true;\n            el.classList.add(\"dragging\");\n            startX = e.pageX - el.offsetLeft;\n            startScroll = el.scrollLeft;\n        };\n        const up = ()=>{\n            isDown = false;\n            el.classList.remove(\"dragging\");\n        };\n        const move = (e)=>{\n            if (!isDown) return;\n            e.preventDefault();\n            const x = e.pageX - el.offsetLeft;\n            const walk = x - startX // px dragged\n            ;\n            el.scrollLeft = startScroll - walk;\n        };\n        // Only enable the mouse drag on non-touch pointers\n        el.addEventListener(\"mousedown\", down);\n        window.addEventListener(\"mouseup\", up);\n        el.addEventListener(\"mousemove\", move);\n        // Allow native touch scrolling without listeners\n        return ()=>{\n            el.removeEventListener(\"mousedown\", down);\n            window.removeEventListener(\"mouseup\", up);\n            el.removeEventListener(\"mousemove\", move);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: wrapRef,\n            className: \"headerstrip-drag mx-auto max-w-6xl overflow-x-auto md:overflow-x-visible\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex md:grid md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-ink/20 min-w-[1100px] md:min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO interior\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"AvroKO\",\n                                image: \"/hero/thumbnail-small.jpeg\",\n                                alt: \"AvroKO interior\",\n                                children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                                title: \"The Roger Hub\",\n                                image: \"/hero/thumbnail-small-1.jpeg\",\n                                alt: \"The Roger Hub\",\n                                children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"UkKPphdwrBVszhnRXq3INm4Aen4=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvSGVhZGVyU3RyaXAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ3lDO0FBQ1g7QUFDRztBQUVqQyxTQUFTSTtJQUNQLHFCQUFPLDhEQUFDQztRQUFLQyxXQUFVO2tCQUFZOzs7Ozs7QUFDckM7S0FGU0Y7QUFXVCxTQUFTRyxrQkFBa0IsS0FBdUQ7UUFBdkQsRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUVDLEdBQUcsRUFBRUMsUUFBUSxFQUEwQixHQUF2RDtJQUN6QixxQkFDRSw4REFBQ0M7UUFBUU4sV0FBVTs7MEJBQ2pCLDhEQUFDTztnQkFDQ1AsV0FBVTs7a0NBR1YsOERBQUNKLGtEQUFLQTt3QkFDSlksS0FBS0w7d0JBQ0xDLEtBQUtBLGdCQUFBQSxpQkFBQUEsTUFBT0Y7d0JBQ1pPLElBQUk7d0JBQ0pULFdBQVU7d0JBQ1ZVLFVBQVU7Ozs7OztrQ0FHWiw4REFBQ0g7d0JBQUlQLFdBQVU7Ozs7Ozs7Ozs7OzswQkFHakIsOERBQUNXO2dCQUFHWCxXQUFVOztvQkFDWEU7b0JBQU07a0NBQUMsOERBQUNKOzs7Ozs7Ozs7OzswQkFFWCw4REFBQ2M7Z0JBQUVaLFdBQVU7MEJBQTBDSzs7Ozs7Ozs7Ozs7O0FBRzdEO01BeEJTSjtBQTBCVCxTQUFTWTtJQUNQLHFCQUNFLDhEQUFDTjtRQUFJUCxXQUFVOzswQkFDYiw4REFBQ087Z0JBQUlQLFdBQVU7MEJBQXNEOzs7Ozs7MEJBQ3JFLDhEQUFDTztnQkFBSVAsV0FBVTs7b0JBQThEO2tDQUN2RCw4REFBQ2M7Ozs7O29CQUFLO2tDQUFrQiw4REFBQ0E7Ozs7O29CQUFLOzs7Ozs7OzBCQUVwRCw4REFBQ1A7Z0JBQUlQLFdBQVU7O2tDQUNiLDhEQUFDZTtrQ0FBTzs7Ozs7O29CQUFhOzs7Ozs7Ozs7Ozs7O0FBSTdCO01BWlNGO0FBY00sU0FBU0c7O0lBQ3RCLE1BQU1DLGFBQWF0Qiw2Q0FBTUEsQ0FBYztJQUN2QyxNQUFNdUIsVUFBVXZCLDZDQUFNQSxDQUFpQjtJQUV2QyxxQ0FBcUM7SUFDckNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlCLFVBQVVGLFdBQVdHLE9BQU87UUFDbEMsSUFBSSxDQUFDRCxTQUFTO1FBRWQsd0NBQXdDO1FBQ3hDdEIsMkNBQUlBLENBQUN3QixJQUFJLENBQUNGLFFBQVFHLGdCQUFnQixDQUFDLHNCQUFzQjtZQUN2REMsU0FBUztZQUFHQyxHQUFHO1lBQUlDLFNBQVM7WUFBTUMsVUFBVTtZQUFLQyxNQUFNO1FBQ3pEO1FBRUEsMkNBQTJDO1FBQzNDLE1BQU1DLFFBQVFDLE1BQU1SLElBQUksQ0FBQ0YsUUFBUUcsZ0JBQWdCLENBQWlCO1FBQ2xFLE1BQU1RLFFBQVEsQ0FBQ0M7WUFDYmxDLDJDQUFJQSxDQUFDbUMsRUFBRSxDQUFDRCxLQUFLO2dCQUFFRSxPQUFPO2dCQUFNUCxVQUFVO2dCQUFNQyxNQUFNO1lBQWE7UUFDakU7UUFDQSxNQUFNTyxRQUFRLENBQUNIO1lBQ2JsQywyQ0FBSUEsQ0FBQ21DLEVBQUUsQ0FBQ0QsS0FBSztnQkFBRUksR0FBRztnQkFBR1gsR0FBRztnQkFBR1ksUUFBUTtnQkFBR0gsT0FBTztnQkFBR1AsVUFBVTtnQkFBTUMsTUFBTTtZQUFhO1FBQ3JGO1FBQ0EsTUFBTVUsT0FBTyxDQUFDTixLQUFrQk87WUFDOUIsTUFBTUMsT0FBTyxJQUFLQyxhQUFhLENBQWlCQyxxQkFBcUI7WUFDckUsTUFBTUMsS0FBSyxDQUFDSixFQUFFSyxPQUFPLEdBQUdKLEtBQUtLLElBQUksSUFBSUwsS0FBS00sS0FBSyxHQUFHLElBQUksWUFBWTs7WUFDbEUsTUFBTUMsS0FBSyxDQUFDUixFQUFFUyxPQUFPLEdBQUdSLEtBQUtTLEdBQUcsSUFBSVQsS0FBS1UsTUFBTSxHQUFHO1lBQ2xEcEQsMkNBQUlBLENBQUNtQyxFQUFFLENBQUNELEtBQUs7Z0JBQ1hJLEdBQUdPLEtBQUs7Z0JBQUlsQixHQUFHc0IsS0FBSztnQkFBSVYsUUFBUU0sS0FBSztnQkFBS1EsV0FBVztnQkFBTXhCLFVBQVU7Z0JBQU1DLE1BQU07WUFDbkY7UUFDRjtRQUVBLE1BQU13QixXQUE4QixFQUFFO1FBQ3RDdkIsTUFBTXdCLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDWixNQUFNdEIsTUFBTXNCLEtBQUtDLGFBQWEsQ0FBQztZQUMvQixJQUFJLENBQUN2QixLQUFLO1lBQ1YsTUFBTXdCLFVBQVUsSUFBTXpCLE1BQU1DO1lBQzVCLE1BQU15QixVQUFVLElBQU10QixNQUFNSDtZQUM1QixNQUFNMEIsU0FBUyxDQUFDbkIsSUFBa0JELEtBQUtOLEtBQUtPO1lBRTVDZSxLQUFLSyxnQkFBZ0IsQ0FBQyxjQUFjSDtZQUNwQ0YsS0FBS0ssZ0JBQWdCLENBQUMsY0FBY0Y7WUFDcENILEtBQUtLLGdCQUFnQixDQUFDLGFBQWFEO1lBQ25DTixTQUFTUSxJQUFJLENBQUM7Z0JBQ1pOLEtBQUtPLG1CQUFtQixDQUFDLGNBQWNMO2dCQUN2Q0YsS0FBS08sbUJBQW1CLENBQUMsY0FBY0o7Z0JBQ3ZDSCxLQUFLTyxtQkFBbUIsQ0FBQyxhQUFhSDtZQUN4QztRQUNGO1FBRUEsT0FBTyxJQUFNTixTQUFTQyxPQUFPLENBQUNTLENBQUFBLEtBQU1BO0lBQ3RDLEdBQUcsRUFBRTtJQUVMLDREQUE0RDtJQUM1RG5FLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTW9FLEtBQUs1QyxRQUFRRSxPQUFPO1FBQzFCLElBQUksQ0FBQzBDLElBQUk7UUFFVCxJQUFJQyxTQUFTO1FBQ2IsSUFBSUMsU0FBUztRQUNiLElBQUlDLGNBQWM7UUFFbEIsTUFBTUMsT0FBTyxDQUFDNUI7WUFDWnlCLFNBQVM7WUFDVEQsR0FBR0ssU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDakJKLFNBQVMxQixFQUFFK0IsS0FBSyxHQUFHUCxHQUFHUSxVQUFVO1lBQ2hDTCxjQUFjSCxHQUFHUyxVQUFVO1FBQzdCO1FBQ0EsTUFBTUMsS0FBSztZQUNUVCxTQUFTO1lBQ1RELEdBQUdLLFNBQVMsQ0FBQ00sTUFBTSxDQUFDO1FBQ3RCO1FBQ0EsTUFBTXBDLE9BQU8sQ0FBQ0M7WUFDWixJQUFJLENBQUN5QixRQUFRO1lBQ2J6QixFQUFFb0MsY0FBYztZQUNoQixNQUFNdkMsSUFBSUcsRUFBRStCLEtBQUssR0FBR1AsR0FBR1EsVUFBVTtZQUNqQyxNQUFNSyxPQUFReEMsSUFBSTZCLE9BQVEsYUFBYTs7WUFDdkNGLEdBQUdTLFVBQVUsR0FBR04sY0FBY1U7UUFDaEM7UUFFQSxtREFBbUQ7UUFDbkRiLEdBQUdKLGdCQUFnQixDQUFDLGFBQWFRO1FBQ2pDVSxPQUFPbEIsZ0JBQWdCLENBQUMsV0FBV2M7UUFDbkNWLEdBQUdKLGdCQUFnQixDQUFDLGFBQWFyQjtRQUVqQyxpREFBaUQ7UUFDakQsT0FBTztZQUNMeUIsR0FBR0YsbUJBQW1CLENBQUMsYUFBYU07WUFDcENVLE9BQU9oQixtQkFBbUIsQ0FBQyxXQUFXWTtZQUN0Q1YsR0FBR0YsbUJBQW1CLENBQUMsYUFBYXZCO1FBQ3RDO0lBQ0YsR0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNsQjtRQUFRMEQsS0FBSzVEO1FBQVlqQixXQUFVO2tCQUVsQyw0RUFBQ087WUFBSXNFLEtBQUszRDtZQUFTbEIsV0FBVTtzQkFFM0IsNEVBQUNPO2dCQUFJUCxXQUFVOztrQ0FDYiw4REFBQ087d0JBQUlQLFdBQVU7d0JBQTBCOEUsYUFBVzs7MENBQ2xELDhEQUFDN0U7Z0NBQ0NDLE9BQU07Z0NBQ05DLE9BQU07Z0NBQ05DLEtBQUk7MENBQ0w7Ozs7OzswQ0FJRCw4REFBQ0g7Z0NBQ0NDLE9BQU07Z0NBQ05DLE9BQU07Z0NBQ05DLEtBQUk7MENBQ0w7Ozs7Ozs7Ozs7OztrQ0FNSCw4REFBQ0c7d0JBQUlQLFdBQVU7d0JBQTBCOEUsYUFBVztrQ0FDbEQsNEVBQUNqRTs7Ozs7Ozs7OztrQ0FHSCw4REFBQ047d0JBQUlQLFdBQVU7d0JBQTBCOEUsYUFBVzs7MENBQ2xELDhEQUFDN0U7Z0NBQ0NDLE9BQU07Z0NBQ05DLE9BQU07Z0NBQ05DLEtBQUk7MENBQ0w7Ozs7OzswQ0FJRCw4REFBQ0g7Z0NBQ0NDLE9BQU07Z0NBQ05DLE9BQU07Z0NBQ05DLEtBQUk7MENBQ0w7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXYjtHQWpKd0JZO01BQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvSGVhZGVyU3RyaXAudHN4PzI0MmIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xyXG5pbXBvcnQgeyBnc2FwIH0gZnJvbSAnQC9saWIvZ3NhcCdcclxuXHJcbmZ1bmN0aW9uIE5ld0JhZGdlKCkge1xyXG4gIHJldHVybiA8c3BhbiBjbGFzc05hbWU9XCJiYWRnZS1uZXdcIj5ORVc8L3NwYW4+XHJcbn1cclxuXHJcbnR5cGUgTWluaUVkaXRvcmlhbENhcmRQcm9wcyA9IHtcclxuICB0aXRsZTogc3RyaW5nXHJcbiAgaW1hZ2U6IHN0cmluZ1xyXG4gIGFsdD86IHN0cmluZ1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufVxyXG5cclxuZnVuY3Rpb24gTWluaUVkaXRvcmlhbENhcmQoeyB0aXRsZSwgaW1hZ2UsIGFsdCwgY2hpbGRyZW4gfTogTWluaUVkaXRvcmlhbENhcmRQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8YXJ0aWNsZSBjbGFzc05hbWU9XCJweC00IG1kOnB4LTYgcHktOFwiPlxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPVwicGFyYWxsYXgtd3JhcCByZWxhdGl2ZSBhc3BlY3QtWzE2LzldIHJvdW5kZWQtbWQgb3ZlcmZsb3ctaGlkZGVuIG1iLTRcIlxyXG4gICAgICA+XHJcbiAgICAgICAgey8qIHRoZSBlbGVtZW50IHdlIGFuaW1hdGUgb24gaG92ZXIgKi99XHJcbiAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICBzcmM9e2ltYWdlfVxyXG4gICAgICAgICAgYWx0PXthbHQgPz8gdGl0bGV9XHJcbiAgICAgICAgICBmaWxsXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJwYXJhbGxheC1pbWcgb2JqZWN0LWNvdmVyIHdpbGwtY2hhbmdlLXRyYW5zZm9ybVwiXHJcbiAgICAgICAgICBwcmlvcml0eT17ZmFsc2V9XHJcbiAgICAgICAgLz5cclxuICAgICAgICB7Lyogb3B0aW9uYWwgc29mdCB2aWduZXR0ZSBmb3IgZGVwdGggKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwb2ludGVyLWV2ZW50cy1ub25lIGFic29sdXRlIGluc2V0LTAgYmctW3JhZGlhbC1ncmFkaWVudCh0cmFuc3BhcmVudF82MCUscmdiYSgwLDAsMCwuMTQpKV0gb3BhY2l0eS00MFwiIC8+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtZGlzcGxheSB0ZXh0LVsyMnB4XSBsZWFkaW5nLW5vbmUgdHJhY2tpbmctWy4wMmVtXSB1cHBlcmNhc2UgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICB7dGl0bGV9IDxOZXdCYWRnZSAvPlxyXG4gICAgICA8L2g0PlxyXG4gICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtWzE1cHhdIGxlYWRpbmctNiB0ZXh0LWluay84MFwiPntjaGlsZHJlbn08L3A+XHJcbiAgICA8L2FydGljbGU+XHJcbiAgKVxyXG59XHJcblxyXG5mdW5jdGlvbiBDZW50ZXJDb3B5KCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgbWQ6cHgtNiBweS04IHRleHQtY2VudGVyIG1kOnRleHQtbGVmdFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtZGlzcGxheSB0ZXh0LTV4bCBtZDp0ZXh0LVs1NnB4XSBsZWFkaW5nLVswLjk1XVwiPkFMTCBXT1JLITwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtZGlzcGxheSB0ZXh0LVsyNnB4XSBtZDp0ZXh0LVszMHB4XSBsZWFkaW5nLVsxLjA1XSBtdC0zXCI+XHJcbiAgICAgICAgQSBGZWF0dXJlZCBzZWxlY3Rpb248YnIgLz4gdGhlIGxhdGVzdCB3b3JrIOKAlDxiciAvPiBvZiB0aGUgbGFzdCB5ZWFycy5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LVsxMnB4XSB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXIgdGV4dC1pbmsvNzBcIj5cclxuICAgICAgICA8c3Ryb25nPlRpcCE8L3N0cm9uZz4gRHJhZyBzaWRld2F5cyB0byBuYXZpZ2F0ZVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVhZGVyU3RyaXAoKSB7XHJcbiAgY29uc3Qgc2VjdGlvblJlZiA9IHVzZVJlZjxIVE1MRWxlbWVudD4obnVsbClcclxuICBjb25zdCB3cmFwUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxyXG5cclxuICAvLyByZXZlYWwgKyBwYXJhbGxheCBob3ZlciBmb3IgaW1hZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHNlY3Rpb24gPSBzZWN0aW9uUmVmLmN1cnJlbnRcclxuICAgIGlmICghc2VjdGlvbikgcmV0dXJuXHJcblxyXG4gICAgLy8gcmV2ZWFsIG9ubHkgY2hpbGRyZW4gKG5vdCBjb250YWluZXJzKVxyXG4gICAgZ3NhcC5mcm9tKHNlY3Rpb24ucXVlcnlTZWxlY3RvckFsbCgnW2RhdGEtcmV2ZWFsXSA+IConKSwge1xyXG4gICAgICBvcGFjaXR5OiAwLCB5OiAxNCwgc3RhZ2dlcjogMC4wNiwgZHVyYXRpb246IDAuNSwgZWFzZTogJ3Bvd2VyMi5vdXQnXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIGltYWdlIGhvdmVyIHBhcmFsbGF4IG9uIGRlc2t0b3AgcG9pbnRlcnNcclxuICAgIGNvbnN0IGNhcmRzID0gQXJyYXkuZnJvbShzZWN0aW9uLnF1ZXJ5U2VsZWN0b3JBbGw8SFRNTERpdkVsZW1lbnQ+KCcucGFyYWxsYXgtd3JhcCcpKVxyXG4gICAgY29uc3QgZW50ZXIgPSAoaW1nOiBIVE1MRWxlbWVudCkgPT4ge1xyXG4gICAgICBnc2FwLnRvKGltZywgeyBzY2FsZTogMS4wNCwgZHVyYXRpb246IDAuMzUsIGVhc2U6ICdwb3dlcjMub3V0JyB9KVxyXG4gICAgfVxyXG4gICAgY29uc3QgbGVhdmUgPSAoaW1nOiBIVE1MRWxlbWVudCkgPT4ge1xyXG4gICAgICBnc2FwLnRvKGltZywgeyB4OiAwLCB5OiAwLCByb3RhdGU6IDAsIHNjYWxlOiAxLCBkdXJhdGlvbjogMC40NSwgZWFzZTogJ3Bvd2VyMy5vdXQnIH0pXHJcbiAgICB9XHJcbiAgICBjb25zdCBtb3ZlID0gKGltZzogSFRNTEVsZW1lbnQsIGU6IE1vdXNlRXZlbnQpID0+IHtcclxuICAgICAgY29uc3QgcmVjdCA9IChpbWcucGFyZW50RWxlbWVudCBhcyBIVE1MRWxlbWVudCkuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KClcclxuICAgICAgY29uc3QgbXggPSAoZS5jbGllbnRYIC0gcmVjdC5sZWZ0KSAvIHJlY3Qud2lkdGggLSAwLjUgLy8gLTAuNS4uMC41XHJcbiAgICAgIGNvbnN0IG15ID0gKGUuY2xpZW50WSAtIHJlY3QudG9wKSAvIHJlY3QuaGVpZ2h0IC0gMC41XHJcbiAgICAgIGdzYXAudG8oaW1nLCB7XHJcbiAgICAgICAgeDogbXggKiAxNCwgeTogbXkgKiAxNCwgcm90YXRlOiBteCAqIDEuMiwgb3ZlcndyaXRlOiB0cnVlLCBkdXJhdGlvbjogMC4yNSwgZWFzZTogJ3Bvd2VyMi5vdXQnXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlcnM6IEFycmF5PCgpID0+IHZvaWQ+ID0gW11cclxuICAgIGNhcmRzLmZvckVhY2goY2FyZCA9PiB7XHJcbiAgICAgIGNvbnN0IGltZyA9IGNhcmQucXVlcnlTZWxlY3RvcignLnBhcmFsbGF4LWltZycpIGFzIEhUTUxFbGVtZW50IHwgbnVsbFxyXG4gICAgICBpZiAoIWltZykgcmV0dXJuXHJcbiAgICAgIGNvbnN0IG9uRW50ZXIgPSAoKSA9PiBlbnRlcihpbWcpXHJcbiAgICAgIGNvbnN0IG9uTGVhdmUgPSAoKSA9PiBsZWF2ZShpbWcpXHJcbiAgICAgIGNvbnN0IG9uTW92ZSA9IChlOiBNb3VzZUV2ZW50KSA9PiBtb3ZlKGltZywgZSlcclxuXHJcbiAgICAgIGNhcmQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2VlbnRlcicsIG9uRW50ZXIpXHJcbiAgICAgIGNhcmQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2VsZWF2ZScsIG9uTGVhdmUpXHJcbiAgICAgIGNhcmQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgb25Nb3ZlKVxyXG4gICAgICBoYW5kbGVycy5wdXNoKCgpID0+IHtcclxuICAgICAgICBjYXJkLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZW50ZXInLCBvbkVudGVyKVxyXG4gICAgICAgIGNhcmQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VsZWF2ZScsIG9uTGVhdmUpXHJcbiAgICAgICAgY2FyZC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZW1vdmUnLCBvbk1vdmUpXHJcbiAgICAgIH0pXHJcbiAgICB9KVxyXG5cclxuICAgIHJldHVybiAoKSA9PiBoYW5kbGVycy5mb3JFYWNoKGZuID0+IGZuKCkpXHJcbiAgfSwgW10pXHJcblxyXG4gIC8vIGNsaWNrLWRyYWcgaG9yaXpvbnRhbCBuYXZpZ2F0aW9uIGZvciBzbWFsbC9tZWRpdW0gc2NyZWVuc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBlbCA9IHdyYXBSZWYuY3VycmVudFxyXG4gICAgaWYgKCFlbCkgcmV0dXJuXHJcblxyXG4gICAgbGV0IGlzRG93biA9IGZhbHNlXHJcbiAgICBsZXQgc3RhcnRYID0gMFxyXG4gICAgbGV0IHN0YXJ0U2Nyb2xsID0gMFxyXG5cclxuICAgIGNvbnN0IGRvd24gPSAoZTogTW91c2VFdmVudCkgPT4ge1xyXG4gICAgICBpc0Rvd24gPSB0cnVlXHJcbiAgICAgIGVsLmNsYXNzTGlzdC5hZGQoJ2RyYWdnaW5nJylcclxuICAgICAgc3RhcnRYID0gZS5wYWdlWCAtIGVsLm9mZnNldExlZnRcclxuICAgICAgc3RhcnRTY3JvbGwgPSBlbC5zY3JvbGxMZWZ0XHJcbiAgICB9XHJcbiAgICBjb25zdCB1cCA9ICgpID0+IHtcclxuICAgICAgaXNEb3duID0gZmFsc2VcclxuICAgICAgZWwuY2xhc3NMaXN0LnJlbW92ZSgnZHJhZ2dpbmcnKVxyXG4gICAgfVxyXG4gICAgY29uc3QgbW92ZSA9IChlOiBNb3VzZUV2ZW50KSA9PiB7XHJcbiAgICAgIGlmICghaXNEb3duKSByZXR1cm5cclxuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXHJcbiAgICAgIGNvbnN0IHggPSBlLnBhZ2VYIC0gZWwub2Zmc2V0TGVmdFxyXG4gICAgICBjb25zdCB3YWxrID0gKHggLSBzdGFydFgpIC8vIHB4IGRyYWdnZWRcclxuICAgICAgZWwuc2Nyb2xsTGVmdCA9IHN0YXJ0U2Nyb2xsIC0gd2Fsa1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE9ubHkgZW5hYmxlIHRoZSBtb3VzZSBkcmFnIG9uIG5vbi10b3VjaCBwb2ludGVyc1xyXG4gICAgZWwuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgZG93bilcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgdXApXHJcbiAgICBlbC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZW1vdmUnLCBtb3ZlKVxyXG5cclxuICAgIC8vIEFsbG93IG5hdGl2ZSB0b3VjaCBzY3JvbGxpbmcgd2l0aG91dCBsaXN0ZW5lcnNcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGVsLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGRvd24pXHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgdXApXHJcbiAgICAgIGVsLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIG1vdmUpXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8c2VjdGlvbiByZWY9e3NlY3Rpb25SZWZ9IGNsYXNzTmFtZT1cIm5vLXdhc2ggcmVsYXRpdmUgei1bM10gYm9yZGVyLXkgYm9yZGVyLWluay8xNVwiPlxyXG4gICAgICB7Lyogd3JhcHBlciBiZWNvbWVzIGhvcml6b250YWxseSBzY3JvbGxhYmxlIG9uIHNtYWxsL21lZGl1bSBzY3JlZW5zICovfVxyXG4gICAgICA8ZGl2IHJlZj17d3JhcFJlZn0gY2xhc3NOYW1lPVwiaGVhZGVyc3RyaXAtZHJhZyBteC1hdXRvIG1heC13LTZ4bCBvdmVyZmxvdy14LWF1dG8gbWQ6b3ZlcmZsb3cteC12aXNpYmxlXCI+XHJcbiAgICAgICAgey8qIFRyYWNrOiByb3cgZm9yIG1vYmlsZS90YWJsZXQ7IGdyaWQgZm9yIGRlc2t0b3AgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1kOmdyaWQgbWQ6Z3JpZC1jb2xzLTMgZGl2aWRlLXkgbWQ6ZGl2aWRlLXktMCBtZDpkaXZpZGUteCBkaXZpZGUtaW5rLzIwIG1pbi13LVsxMTAwcHhdIG1kOm1pbi13LTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctWzg1dnddIG1kOm1pbi13LTBcIiBkYXRhLXJldmVhbD5cclxuICAgICAgICAgICAgPE1pbmlFZGl0b3JpYWxDYXJkXHJcbiAgICAgICAgICAgICAgdGl0bGU9XCJBdnJvS09cIlxyXG4gICAgICAgICAgICAgIGltYWdlPVwiL2hlcm8vdGh1bWJuYWlsLXNtYWxsLmpwZWdcIlxyXG4gICAgICAgICAgICAgIGFsdD1cIkF2cm9LTyBpbnRlcmlvclwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBBdnJvS08gaXMgYW4gYXdhcmQtd2lubmluZyBnbG9iYWwgZGVzaWduIGZpcm0sIGVzdGFibGlzaGVkIGl0c2VsZiBhcyBhIGdsb2JhbCBsZWFkZXIgaW4gaW50ZXJpb3IgYXJjaGl0ZWN0dXJlXHJcbiAgICAgICAgICAgICAgZm9yIGhvc3BpdGFsaXR5LCByZXN0YXVyYW50IGFuZCBiYXJzLlxyXG4gICAgICAgICAgICA8L01pbmlFZGl0b3JpYWxDYXJkPlxyXG4gICAgICAgICAgICA8TWluaUVkaXRvcmlhbENhcmRcclxuICAgICAgICAgICAgICB0aXRsZT1cIkF2cm9LT1wiXHJcbiAgICAgICAgICAgICAgaW1hZ2U9XCIvaGVyby90aHVtYm5haWwtc21hbGwuanBlZ1wiXHJcbiAgICAgICAgICAgICAgYWx0PVwiQXZyb0tPIGludGVyaW9yXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIEF2cm9LTyBpcyBhbiBhd2FyZC13aW5uaW5nIGdsb2JhbCBkZXNpZ24gZmlybSwgZXN0YWJsaXNoZWQgaXRzZWxmIGFzIGEgZ2xvYmFsIGxlYWRlciBpbiBpbnRlcmlvciBhcmNoaXRlY3R1cmVcclxuICAgICAgICAgICAgICBmb3IgaG9zcGl0YWxpdHksIHJlc3RhdXJhbnQgYW5kIGJhcnMuXHJcbiAgICAgICAgICAgIDwvTWluaUVkaXRvcmlhbENhcmQ+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LVs4NXZ3XSBtZDptaW4tdy0wXCIgZGF0YS1yZXZlYWw+XHJcbiAgICAgICAgICAgIDxDZW50ZXJDb3B5IC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LVs4NXZ3XSBtZDptaW4tdy0wXCIgZGF0YS1yZXZlYWw+XHJcbiAgICAgICAgICAgIDxNaW5pRWRpdG9yaWFsQ2FyZFxyXG4gICAgICAgICAgICAgIHRpdGxlPVwiVGhlIFJvZ2VyIEh1YlwiXHJcbiAgICAgICAgICAgICAgaW1hZ2U9XCIvaGVyby90aHVtYm5haWwtc21hbGwtMS5qcGVnXCJcclxuICAgICAgICAgICAgICBhbHQ9XCJUaGUgUm9nZXIgSHViXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIFRoZSBSb2dlciBIdWIgaXMgYW4gaW1tZXJzaXZlIHdlYiBleHBlcmllbmNlIHNob3djYXNpbmcgdGhlIHRlbm5pcy1pbnNwaXJlZCDigJhPbuKAmSBzbmVha2VycywgYSBjb2xsYWJvcmF0aW9uXHJcbiAgICAgICAgICAgICAgYm9ybiBvdXQgb2YgYSBwYXJ0bmVyc2hpcCB3aXRoIHRoZSBsZWdlbmRhcnkgUm9nZXIgRmVkZXJlci5cclxuICAgICAgICAgICAgPC9NaW5pRWRpdG9yaWFsQ2FyZD5cclxuICAgICAgICAgICAgPE1pbmlFZGl0b3JpYWxDYXJkXHJcbiAgICAgICAgICAgICAgdGl0bGU9XCJUaGUgUm9nZXIgSHViXCJcclxuICAgICAgICAgICAgICBpbWFnZT1cIi9oZXJvL3RodW1ibmFpbC1zbWFsbC0xLmpwZWdcIlxyXG4gICAgICAgICAgICAgIGFsdD1cIlRoZSBSb2dlciBIdWJcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgVGhlIFJvZ2VyIEh1YiBpcyBhbiBpbW1lcnNpdmUgd2ViIGV4cGVyaWVuY2Ugc2hvd2Nhc2luZyB0aGUgdGVubmlzLWluc3BpcmVkIOKAmE9u4oCZIHNuZWFrZXJzLCBhIGNvbGxhYm9yYXRpb25cclxuICAgICAgICAgICAgICBib3JuIG91dCBvZiBhIHBhcnRuZXJzaGlwIHdpdGggdGhlIGxlZ2VuZGFyeSBSb2dlciBGZWRlcmVyLlxyXG4gICAgICAgICAgICA8L01pbmlFZGl0b3JpYWxDYXJkPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICBcclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L3NlY3Rpb24+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJJbWFnZSIsImdzYXAiLCJOZXdCYWRnZSIsInNwYW4iLCJjbGFzc05hbWUiLCJNaW5pRWRpdG9yaWFsQ2FyZCIsInRpdGxlIiwiaW1hZ2UiLCJhbHQiLCJjaGlsZHJlbiIsImFydGljbGUiLCJkaXYiLCJzcmMiLCJmaWxsIiwicHJpb3JpdHkiLCJoNCIsInAiLCJDZW50ZXJDb3B5IiwiYnIiLCJzdHJvbmciLCJIZWFkZXJTdHJpcCIsInNlY3Rpb25SZWYiLCJ3cmFwUmVmIiwic2VjdGlvbiIsImN1cnJlbnQiLCJmcm9tIiwicXVlcnlTZWxlY3RvckFsbCIsIm9wYWNpdHkiLCJ5Iiwic3RhZ2dlciIsImR1cmF0aW9uIiwiZWFzZSIsImNhcmRzIiwiQXJyYXkiLCJlbnRlciIsImltZyIsInRvIiwic2NhbGUiLCJsZWF2ZSIsIngiLCJyb3RhdGUiLCJtb3ZlIiwiZSIsInJlY3QiLCJwYXJlbnRFbGVtZW50IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwibXgiLCJjbGllbnRYIiwibGVmdCIsIndpZHRoIiwibXkiLCJjbGllbnRZIiwidG9wIiwiaGVpZ2h0Iiwib3ZlcndyaXRlIiwiaGFuZGxlcnMiLCJmb3JFYWNoIiwiY2FyZCIsInF1ZXJ5U2VsZWN0b3IiLCJvbkVudGVyIiwib25MZWF2ZSIsIm9uTW92ZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJwdXNoIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImZuIiwiZWwiLCJpc0Rvd24iLCJzdGFydFgiLCJzdGFydFNjcm9sbCIsImRvd24iLCJjbGFzc0xpc3QiLCJhZGQiLCJwYWdlWCIsIm9mZnNldExlZnQiLCJzY3JvbGxMZWZ0IiwidXAiLCJyZW1vdmUiLCJwcmV2ZW50RGVmYXVsdCIsIndhbGsiLCJ3aW5kb3ciLCJyZWYiLCJkYXRhLXJldmVhbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});