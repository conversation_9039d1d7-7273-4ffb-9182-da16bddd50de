"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst NavLink = (param)=>{\n    let { href, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: href,\n        className: \"magnet group inline-flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"tracking-loose2\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block h-px w-6 bg-ink/40 transition-all group-hover:w-10\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n};\n_c = NavLink;\nfunction Header() {\n    _s();\n    const headerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // subtle reveal\n        if (headerRef.current) {\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(headerRef.current.querySelectorAll(\"[data-reveal] > *\"), {\n                opacity: 0,\n                y: 14,\n                stagger: 0.06,\n                duration: 0.5,\n                ease: \"power2.out\"\n            });\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        ref: headerRef,\n        className: \"sticky top-0 z-40 bg-paper supports-[backdrop-filter]:bg-paper/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-6xl px-4 py-4 flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    className: \"doodle-wrap flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Kaitare, Richard\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"doodle-stroke w-10 h-8\",\n                            viewBox: \"0 0 100 80\",\n                            \"aria-hidden\": true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M10 40 Q 50 5, 90 40 Q 50 75, 10 40 z\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"site-nav\", \"flex items-center gap-6 text-sm\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/work\",\n                            children: \"Work\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                            href: \"/about\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\Header.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"XD/jeNJYZIWi7FSDMCiT0Bg52BI=\");\n_c1 = Header;\nvar _c, _c1;\n$RefreshReg$(_c, \"NavLink\");\n$RefreshReg$(_c1, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Header.tsx\n"));

/***/ })

});