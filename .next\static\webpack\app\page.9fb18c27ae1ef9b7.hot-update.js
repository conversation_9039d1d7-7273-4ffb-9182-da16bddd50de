"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst tiles = [\n    {\n        kind: \"card\",\n        title: \"AvroKO\",\n        image: \"/hero/thumbnail-small-3.jpeg\",\n        body: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n    },\n    {\n        kind: \"copy\"\n    },\n    {\n        kind: \"card\",\n        title: \"The Roger Hub\",\n        image: \"/hero/thumbnail-small-1.jpeg\",\n        body: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n    },\n    // add a couple extra tiles so the rail definitely overflows on desktop\n    {\n        kind: \"card\",\n        title: \"WOW Concept\",\n        image: \"/hero/thumbnail-small-3.jpeg\",\n        body: \"A concept store experience in Madrid revolutionizing retail with dynamic, interactive shopping.\"\n    },\n    {\n        kind: \"card\",\n        title: \"Unexpected Time\",\n        image: \"/hero/thumbnail-small-1.jpeg\",\n        body: \"Timeless design & details – a creative portfolio that evolves with modern functionality.\"\n    }\n];\nfunction HeaderStrip() {\n    _s();\n    const viewportRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const railRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // DRAGGABLE rail with snap\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const viewport = viewportRef.current;\n        const rail = railRef.current;\n        const tilesEls = Array.from(rail.querySelectorAll(\".hs-tile\"));\n        // set tile widths so the rail always exceeds viewport width\n        // (CSS also handles this, but we clamp again here after fonts load)\n        const setWidths = ()=>{\n            tilesEls.forEach((t)=>{\n            // nothing to compute; CSS flex-basis handles it, but we keep for clarity\n            });\n        };\n        // position rail at start\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.set(rail, {\n            x: 0\n        });\n        const minX = ()=>Math.min(0, viewport.clientWidth - rail.scrollWidth);\n        const draggable = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.Draggable.create(rail, {\n            type: \"x\",\n            bounds: {\n                minX: minX(),\n                maxX: 0\n            },\n            inertia: false,\n            dragClickables: true,\n            onPress () {\n                viewport.classList.add(\"dragging\");\n            },\n            onRelease () {\n                viewport.classList.remove(\"dragging\");\n                snapToClosest();\n            }\n        })[0];\n        // snap to nearest tile so a tile aligns to the left edge\n        function snapToClosest() {\n            const x = draggable.x;\n            let best = 0, min = Infinity;\n            for (const t of tilesEls){\n                const goal = -t.offsetLeft // left-align this tile\n                ;\n                const d = Math.abs(x - goal);\n                if (d < min) {\n                    min = d;\n                    best = goal;\n                }\n            }\n            const target = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.clamp(minX(), 0, best);\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(rail, {\n                x: target,\n                duration: 0.35,\n                ease: \"power3.out\",\n                onUpdate: ()=>draggable.update()\n            });\n        }\n        // wheel → horizontal (great on trackpads)\n        let wheelSnapTimer;\n        const onWheel = (e)=>{\n            if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {\n                const next = _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.utils.clamp(minX(), 0, draggable.x - e.deltaY);\n                _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(rail, {\n                    x: next,\n                    duration: 0.12,\n                    ease: \"power2.out\",\n                    onUpdate: ()=>draggable.update()\n                });\n                e.preventDefault();\n                clearTimeout(wheelSnapTimer);\n                wheelSnapTimer = setTimeout(snapToClosest, 140);\n            }\n        };\n        viewport.addEventListener(\"wheel\", onWheel, {\n            passive: false\n        });\n        // keep bounds correct on resize/font load\n        const ro = new ResizeObserver(()=>{\n            draggable.applyBounds({\n                minX: minX(),\n                maxX: 0\n            });\n        });\n        ro.observe(viewport);\n        ro.observe(rail);\n        setWidths();\n        return ()=>{\n            viewport.removeEventListener(\"wheel\", onWheel);\n            ro.disconnect();\n            draggable.kill();\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: viewportRef,\n            className: \"hs-viewport mx-auto max-w-6xl overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: railRef,\n                className: \"hs-rail flex gap-0 select-none\",\n                children: tiles.map((t, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hs-tile border-l first:border-l-0 border-ink/20\",\n                        children: t.kind === \"copy\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 md:px-6 py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                                    children: \"ALL WORK!\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                                    children: [\n                                        \"A Featured selection\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 41\n                                        }, this),\n                                        \" the latest work —\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 64\n                                        }, this),\n                                        \" of the last years.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Tip!\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 21\n                                        }, this),\n                                        \" Drag sideways to navigate\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"px-4 md:px-6 py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative aspect-[16/9] rounded-lg overflow-hidden mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: t.image,\n                                            alt: t.title,\n                                            fill: true,\n                                            className: \"object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-35\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-display text-[22px] uppercase leading-none tracking-[.02em] flex items-center gap-2\",\n                                    children: [\n                                        t.title,\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"badge-new\",\n                                            children: \"NEW\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                                    children: t.body\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 17\n                        }, this)\n                    }, i, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"4nUAO7fvWph9p+kKBG2TktslihA=\");\n_c = HeaderStrip;\nvar _c;\n$RefreshReg$(_c, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});