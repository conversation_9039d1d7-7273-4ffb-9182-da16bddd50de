"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HeaderStrip.tsx":
/*!************************************!*\
  !*** ./components/HeaderStrip.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeaderStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gsap */ \"(app-pages-browser)/./lib/gsap.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewBadge() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"badge-new\",\n        children: \"NEW\"\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n_c = NewBadge;\nfunction MiniEditorialCard(param) {\n    let { title, image, alt, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"px-4 md:px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"parallax-wrap relative aspect-[16/9] rounded-md overflow-hidden mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: image,\n                        alt: alt !== null && alt !== void 0 ? alt : title,\n                        fill: true,\n                        className: \"parallax-img object-cover will-change-transform\",\n                        priority: false\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-display text-[22px] leading-none tracking-[.02em] uppercase flex items-center gap-2\",\n                children: [\n                    title,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewBadge, {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-[15px] leading-6 text-ink/80\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c1 = MiniEditorialCard;\nfunction CenterCopy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 md:px-6 py-8 text-center md:text-left\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-5xl md:text-[56px] leading-[0.95]\",\n                children: \"ALL WORK!\"\n            }, void 0, false, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-display text-[26px] md:text-[30px] leading-[1.05] mt-3\",\n                children: [\n                    \"A Featured selection\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 29\n                    }, this),\n                    \" the latest work —\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 53\n                    }, this),\n                    \" of the last years.\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-[12px] uppercase tracking-wider text-ink/70\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Tip!\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    \" Drag sideways to navigate\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CenterCopy;\nfunction HeaderStrip() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const wrapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // reveal + parallax hover for images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const section = sectionRef.current;\n        if (!section) return;\n        // reveal only children (not containers)\n        _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.from(section.querySelectorAll(\"[data-reveal] > *\"), {\n            opacity: 0,\n            y: 14,\n            stagger: 0.06,\n            duration: 0.5,\n            ease: \"power2.out\"\n        });\n        // image hover parallax on desktop pointers\n        const cards = Array.from(section.querySelectorAll(\".parallax-wrap\"));\n        const enter = (img)=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                scale: 1.04,\n                duration: 0.35,\n                ease: \"power3.out\"\n            });\n        };\n        const leave = (img)=>{\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: 0,\n                y: 0,\n                rotate: 0,\n                scale: 1,\n                duration: 0.45,\n                ease: \"power3.out\"\n            });\n        };\n        const move = (img, e)=>{\n            const rect = img.parentElement.getBoundingClientRect();\n            const mx = (e.clientX - rect.left) / rect.width - 0.5 // -0.5..0.5\n            ;\n            const my = (e.clientY - rect.top) / rect.height - 0.5;\n            _lib_gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(img, {\n                x: mx * 14,\n                y: my * 14,\n                rotate: mx * 1.2,\n                overwrite: true,\n                duration: 0.25,\n                ease: \"power2.out\"\n            });\n        };\n        const handlers = [];\n        cards.forEach((card)=>{\n            const img = card.querySelector(\".parallax-img\");\n            if (!img) return;\n            const onEnter = ()=>enter(img);\n            const onLeave = ()=>leave(img);\n            const onMove = (e)=>move(img, e);\n            card.addEventListener(\"mouseenter\", onEnter);\n            card.addEventListener(\"mouseleave\", onLeave);\n            card.addEventListener(\"mousemove\", onMove);\n            handlers.push(()=>{\n                card.removeEventListener(\"mouseenter\", onEnter);\n                card.removeEventListener(\"mouseleave\", onLeave);\n                card.removeEventListener(\"mousemove\", onMove);\n            });\n        });\n        return ()=>handlers.forEach((fn)=>fn());\n    }, []);\n    // click-drag horizontal navigation for small/medium screens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = wrapRef.current;\n        if (!el) return;\n        let isDown = false;\n        let startX = 0;\n        let startScroll = 0;\n        const down = (e)=>{\n            isDown = true;\n            el.classList.add(\"dragging\");\n            startX = e.pageX - el.offsetLeft;\n            startScroll = el.scrollLeft;\n        };\n        const up = ()=>{\n            isDown = false;\n            el.classList.remove(\"dragging\");\n        };\n        const move = (e)=>{\n            if (!isDown) return;\n            e.preventDefault();\n            const x = e.pageX - el.offsetLeft;\n            const walk = x - startX // px dragged\n            ;\n            el.scrollLeft = startScroll - walk;\n        };\n        // Only enable the mouse drag on non-touch pointers\n        el.addEventListener(\"mousedown\", down);\n        window.addEventListener(\"mouseup\", up);\n        el.addEventListener(\"mousemove\", move);\n        // Allow native touch scrolling without listeners\n        return ()=>{\n            el.removeEventListener(\"mousedown\", down);\n            window.removeEventListener(\"mouseup\", up);\n            el.removeEventListener(\"mousemove\", move);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"no-wash relative z-[3] border-y border-ink/15\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: wrapRef,\n            className: \"headerstrip-drag mx-auto max-w-6xl overflow-x-auto md:overflow-x-visible\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex md:grid md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-ink/20 min-w-[1100px] md:min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                            title: \"AvroKO\",\n                            image: \"/hero/thumbnail-small.jpeg\",\n                            alt: \"AvroKO interior\",\n                            children: \"AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture for hospitality, restaurant and bars.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterCopy, {}, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-[85vw] md:min-w-0\",\n                        \"data-reveal\": true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniEditorialCard, {\n                            title: \"The Roger Hub\",\n                            image: \"/hero/thumbnail-small-1.jpeg\",\n                            alt: \"The Roger Hub\",\n                            children: \"The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration born out of a partnership with the legendary Roger Federer.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding projects\\\\Next js\\\\richport\\\\components\\\\HeaderStrip.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(HeaderStrip, \"UkKPphdwrBVszhnRXq3INm4Aen4=\");\n_c3 = HeaderStrip;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"NewBadge\");\n$RefreshReg$(_c1, \"MiniEditorialCard\");\n$RefreshReg$(_c2, \"CenterCopy\");\n$RefreshReg$(_c3, \"HeaderStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HeaderStrip.tsx\n"));

/***/ })

});