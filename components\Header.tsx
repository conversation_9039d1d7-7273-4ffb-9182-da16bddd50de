'use client'
import Link from 'next/link'
import { useEffect, useRef } from 'react'
import { gsap } from '@/lib/gsap'
import clsx from 'clsx'
import TopBar from '@/components/TopBar'
import type { Route } from 'next'

const NavLink = ({ href, children }: { href: Route, children: React.ReactNode }) => (
  <Link href={href} className="magnet group inline-flex items-center gap-2">
    <span className="tracking-loose2">{children}</span>
    <span className="block h-px w-6 bg-ink/40 transition-all group-hover:w-10" />
  </Link>
)


export default function Header() {
  const headerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    // subtle reveal
    if (headerRef.current) {
      gsap.from(headerRef.current.querySelectorAll('[data-reveal] > *'), {
        opacity: 0, y: 14, stagger: 0.06, duration: 0.5, ease: 'power2.out'
      })
    }
  }, [])

  return (
    <header ref={headerRef} className="sticky top-0 z-40 bg-paper">
      <div className="mx-auto max-w-6xl px-4 py-4 flex items-center justify-between">
        <Link href="/" className="doodle-wrap flex items-center gap-3">
          <span className="text-sm">Kaitare, Richard</span>
          <svg className="doodle-stroke w-10 h-8" viewBox="0 0 100 80" aria-hidden>
            <path d="M10 40 Q 50 5, 90 40 Q 50 75, 10 40 z" />
          </svg>
        </Link>
        <nav className={clsx('site-nav','flex items-center gap-6 text-sm')}>
          <NavLink href="/">Home</NavLink>
          <NavLink href="/work">Work</NavLink>
          <NavLink href="/about">About</NavLink>
        </nav>
      </div>
    </header>
  )
}
